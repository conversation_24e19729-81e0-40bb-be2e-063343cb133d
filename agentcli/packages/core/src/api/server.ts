import express, { Express, Request, Response } from 'express';
import { createServer } from 'http';
import cors from 'cors';
import { AgentWebSocketServer } from './websocket-server.js';
import { AgentBuilder } from '../agent_builder/agent-builder.js';
import { AgentTemplateManager } from '../agent_builder/template-manager.js';

export interface ServerConfig {
  port: number;
  corsOrigin?: string;
}

export class AgentAPIServer {
  private app: Express;
  private httpServer: any;
  private wsServer: AgentWebSocketServer;
  private agentBuilder: AgentBuilder;
  private templateManager: AgentTemplateManager;
  private port: number;

  constructor(config: ServerConfig) {
    this.port = config.port;
    this.app = express();
    this.httpServer = createServer(this.app);
    this.wsServer = new AgentWebSocketServer(this.httpServer);
    this.agentBuilder = new AgentBuilder();
    this.templateManager = new AgentTemplateManager();

    this.setupMiddleware(config.corsOrigin);
    this.setupRoutes();
    this.setupWebSocketHandlers();
  }

  private setupMiddleware(corsOrigin?: string) {
    this.app.use(cors({
      origin: corsOrigin || 'http://localhost:3000',
      credentials: true
    }));
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
  }

  private setupRoutes() {
    // Health check
    this.app.get('/api/health', (req: Request, res: Response) => {
      res.json({ status: 'ok', timestamp: new Date() });
    });

    // Get available agent templates
    this.app.get('/api/templates', async (req: Request, res: Response) => {
      try {
        const templates = await this.templateManager.getAllTemplates();
        res.json({ templates });
      } catch (error) {
        res.status(500).json({ error: 'Failed to fetch templates' });
      }
    });

    // Get template details
    this.app.get('/api/templates/:id', async (req: Request, res: Response) => {
      try {
        const template = await this.templateManager.getTemplate(req.params.id);
        if (!template) {
          return res.status(404).json({ error: 'Template not found' });
        }
        res.json({ template });
      } catch (error) {
        res.status(500).json({ error: 'Failed to fetch template' });
      }
    });

    // Create new agent
    this.app.post('/api/agents', async (req: Request, res: Response) => {
      try {
        const { name, description, templateId, config } = req.body;
        const agent = await this.agentBuilder.createAgent({
          name,
          description,
          templateId,
          config
        });
        res.json({ agent });
      } catch (error) {
        res.status(500).json({ error: 'Failed to create agent' });
      }
    });

    // Get user's agents
    this.app.get('/api/agents', async (req: Request, res: Response) => {
      try {
        const userId = req.headers['x-user-id'] as string;
        const agents = await this.agentBuilder.getUserAgents(userId);
        res.json({ agents });
      } catch (error) {
        res.status(500).json({ error: 'Failed to fetch agents' });
      }
    });

    // Get agent details
    this.app.get('/api/agents/:id', async (req: Request, res: Response) => {
      try {
        const agent = await this.agentBuilder.getAgent(req.params.id);
        if (!agent) {
          return res.status(404).json({ error: 'Agent not found' });
        }
        res.json({ agent });
      } catch (error) {
        res.status(500).json({ error: 'Failed to fetch agent' });
      }
    });

    // Update agent
    this.app.put('/api/agents/:id', async (req: Request, res: Response) => {
      try {
        const agent = await this.agentBuilder.updateAgent(req.params.id, req.body);
        res.json({ agent });
      } catch (error) {
        res.status(500).json({ error: 'Failed to update agent' });
      }
    });

    // Delete agent
    this.app.delete('/api/agents/:id', async (req: Request, res: Response) => {
      try {
        await this.agentBuilder.deleteAgent(req.params.id);
        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: 'Failed to delete agent' });
      }
    });

    // Get agent metrics
    this.app.get('/api/agents/:id/metrics', async (req: Request, res: Response) => {
      try {
        const metrics = await this.agentBuilder.getAgentMetrics(req.params.id);
        res.json({ metrics });
      } catch (error) {
        res.status(500).json({ error: 'Failed to fetch metrics' });
      }
    });

    // Execute agent task (for testing)
    this.app.post('/api/agents/:id/execute', async (req: Request, res: Response) => {
      try {
        const { task, parameters } = req.body;
        const result = await this.agentBuilder.executeAgentTask(req.params.id, task, parameters);
        res.json({ result });
      } catch (error) {
        res.status(500).json({ error: 'Failed to execute task' });
      }
    });
  }

  private setupWebSocketHandlers() {
    // Handle agent building through WebSocket
    this.wsServer.on('build-agent', async (data) => {
      const { sessionId, config, template } = data;
      
      try {
        // Start building process
        this.wsServer.emitProgress({
          sessionId,
          status: 'initializing',
          progress: 0,
          currentStep: 'Initializing agent builder...',
          logs: [],
          timestamp: new Date()
        });

        // Build the agent with progress updates
        await this.agentBuilder.buildWithProgress(
          config,
          template,
          (progress) => {
            this.wsServer.emitProgress({
              sessionId,
              ...progress,
              timestamp: new Date()
            });
          },
          (thought) => {
            this.wsServer.emitAgentThought({
              sessionId,
              thought: thought.content,
              confidence: thought.confidence,
              timestamp: new Date()
            });
          }
        );

        // Notify completion
        this.wsServer.emitProgress({
          sessionId,
          status: 'completed',
          progress: 100,
          currentStep: 'Agent built successfully!',
          logs: [],
          timestamp: new Date()
        });
      } catch (error) {
        this.wsServer.emitError(sessionId, error.message);
      }
    });

    // Handle tool execution
    this.wsServer.on('execute-tool', async (data) => {
      const { sessionId, toolName, parameters } = data;
      
      try {
        this.wsServer.emitToolExecution({
          sessionId,
          toolName,
          status: 'started',
          timestamp: new Date()
        });

        // Execute tool with streaming output
        await this.agentBuilder.executeTool(
          toolName,
          parameters,
          (output) => {
            this.wsServer.emitToolExecution({
              sessionId,
              toolName,
              status: 'running',
              output,
              timestamp: new Date()
            });
          }
        );

        this.wsServer.emitToolExecution({
          sessionId,
          toolName,
          status: 'completed',
          timestamp: new Date()
        });
      } catch (error) {
        this.wsServer.emitToolExecution({
          sessionId,
          toolName,
          status: 'error',
          error: error.message,
          timestamp: new Date()
        });
      }
    });

    // Handle agent messages
    this.wsServer.on('agent-message', async (data) => {
      const { sessionId, message } = data;
      
      try {
        // Process message through agent
        const response = await this.agentBuilder.processMessage(sessionId, message);
        this.wsServer.emitConsoleOutput(sessionId, response);
      } catch (error) {
        this.wsServer.emitError(sessionId, error.message);
      }
    });
  }

  async start() {
    return new Promise((resolve) => {
      this.httpServer.listen(this.port, () => {
        console.log(`Agent API Server running on port ${this.port}`);
        resolve(true);
      });
    });
  }

  async stop() {
    this.wsServer.close();
    return new Promise((resolve) => {
      this.httpServer.close(() => {
        console.log('Agent API Server stopped');
        resolve(true);
      });
    });
  }
}