import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { EventEmitter } from 'events';

export interface AgentBuildProgress {
  sessionId: string;
  status: 'initializing' | 'building' | 'testing' | 'deploying' | 'completed' | 'error';
  progress: number;
  currentStep: string;
  logs: string[];
  timestamp: Date;
}

export interface ToolExecutionEvent {
  sessionId: string;
  toolName: string;
  status: 'started' | 'running' | 'completed' | 'error';
  output?: string;
  error?: string;
  timestamp: Date;
}

export interface AgentThought {
  sessionId: string;
  thought: string;
  confidence: number;
  timestamp: Date;
}

export class AgentWebSocketServer extends EventEmitter {
  private io: SocketIOServer;
  private sessions: Map<string, any> = new Map();

  constructor(httpServer: HTTPServer) {
    super();
    
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || 'http://localhost:3000',
        methods: ['GET', 'POST']
      }
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log('Client connected:', socket.id);
      
      // Handle session initialization
      socket.on('init-session', (data: { userId: string; agentType: string }) => {
        const sessionId = this.generateSessionId();
        const session = {
          id: sessionId,
          socketId: socket.id,
          userId: data.userId,
          agentType: data.agentType,
          startTime: new Date(),
          status: 'initializing'
        };
        
        this.sessions.set(sessionId, session);
        socket.join(sessionId);
        
        socket.emit('session-created', { sessionId });
        this.emit('session-created', session);
      });
      
      // Handle agent building requests
      socket.on('build-agent', (data: { 
        sessionId: string; 
        config: any;
        template?: string;
      }) => {
        this.emit('build-agent', data);
      });
      
      // Handle tool execution requests
      socket.on('execute-tool', (data: {
        sessionId: string;
        toolName: string;
        parameters: any;
      }) => {
        this.emit('execute-tool', data);
      });
      
      // Handle chat messages for agent interaction
      socket.on('agent-message', (data: {
        sessionId: string;
        message: string;
      }) => {
        this.emit('agent-message', data);
      });
      
      // Handle disconnection
      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
        // Clean up sessions associated with this socket
        for (const [sessionId, session] of this.sessions.entries()) {
          if (session.socketId === socket.id) {
            this.sessions.delete(sessionId);
            this.emit('session-ended', { sessionId });
          }
        }
      });
    });
  }

  // Emit progress updates to clients
  emitProgress(progress: AgentBuildProgress) {
    this.io.to(progress.sessionId).emit('build-progress', progress);
  }

  // Emit tool execution events
  emitToolExecution(event: ToolExecutionEvent) {
    this.io.to(event.sessionId).emit('tool-execution', event);
  }

  // Emit agent thoughts (thinking process)
  emitAgentThought(thought: AgentThought) {
    this.io.to(thought.sessionId).emit('agent-thought', thought);
  }

  // Emit console output
  emitConsoleOutput(sessionId: string, output: string) {
    this.io.to(sessionId).emit('console-output', {
      sessionId,
      output,
      timestamp: new Date()
    });
  }

  // Emit errors
  emitError(sessionId: string, error: string) {
    this.io.to(sessionId).emit('error', {
      sessionId,
      error,
      timestamp: new Date()
    });
  }

  // Get active sessions
  getActiveSessions(): Map<string, any> {
    return new Map(this.sessions);
  }

  // Generate unique session ID
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Close server
  close() {
    this.io.close();
  }
}