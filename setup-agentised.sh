#!/bin/bash

# Simple setup for agentised organization
echo "🤖 Setting up agentised repositories..."
echo "======================================"

# Create a workspace directory
mkdir -p ~/agentised
cd ~/agentised

# 1. Landing Page Repository
echo "📄 Creating landing page repository..."
gh repo create agentised/landingpage --private --clone \
  --description "Landing page for agentised AI platform"
cd landingpage
echo "# agentised Landing Page" > README.md
echo "node_modules/
.env
.DS_Store
dist/
.next/" > .gitignore
git add . && git commit -m "Initial commit" && git push origin main
cd ..

# 2. Frontend Repository  
echo "💻 Creating frontend repository..."
gh repo create agentised/frontend --private --clone \
  --description "Main application frontend"
cd frontend
echo "# agentised Frontend" > README.md
echo "node_modules/
.env
.env.local
build/
dist/
.DS_Store" > .gitignore
git add . && git commit -m "Initial commit" && git push origin main
cd ..

# 3. Backend Repository
echo "🔧 Creating backend repository..."
gh repo create agentised/backend --private --clone \
  --description "API and backend services"
cd backend
echo "# agentised Backend" > README.md
echo "node_modules/
.env
dist/
.DS_Store
*.log" > .gitignore
git add . && git commit -m "Initial commit" && git push origin main
cd ..

# Optional: Shared utilities
echo "📦 Creating shared repository..."
gh repo create agentised/shared --private --clone \
  --description "Shared types and utilities"
cd shared
echo "# agentised Shared" > README.md
echo "node_modules/
dist/
.DS_Store" > .gitignore
git add . && git commit -m "Initial commit" && git push origin main
cd ..

echo "✅ All repositories created!"
echo ""
echo "📁 Your repositories:"
echo "  • https://github.com/agentised/landingpage (public)"
echo "  • https://github.com/agentised/frontend (private)"
echo "  • https://github.com/agentised/backend (private)"
echo "  • https://github.com/agentised/shared (private)"