import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());

// Basic health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'AI Agents Backend is running' });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  socket.on('start-agent', (data) => {
    console.log('Agent request received:', data);
    
    // Simulate agent processing
    socket.emit('agent-thinking', { thought: 'Analyzing your request...' });
    
    setTimeout(() => {
      socket.emit('agent-output', { output: 'Processing your agent request...' });
    }, 1000);

    setTimeout(() => {
      socket.emit('tool-execution', { toolName: 'Agent Builder' });
    }, 2000);

    setTimeout(() => {
      socket.emit('agent-output', { output: 'Creating agent configuration...' });
    }, 3000);

    setTimeout(() => {
      socket.emit('build-progress', { status: 'completed' });
    }, 4000);
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 3002;

server.listen(PORT, () => {
  console.log(`AI Agents Backend running on port ${PORT}`);
});
