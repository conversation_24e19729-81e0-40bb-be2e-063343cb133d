<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Agents for No-Code Business</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", sans-serif;
            background: #000;
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        html {
            scroll-behavior: smooth;
        }

        section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .gradient-bg {
            position: fixed;
            width: 200%;
            height: 200%;
            background: radial-gradient(ellipse at center, rgba(255,255,255,0.03) 0%, transparent 70%);
            animation: rotate 30s linear infinite;
            pointer-events: none;
            top: -50%;
            left: -50%;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .container {
            width: 100%;
            max-width: 600px;
            padding: 20px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        h1 {
            font-size: clamp(2.5rem, 8vw, 4rem);
            font-weight: 700;
            letter-spacing: -0.04em;
            margin-bottom: 1rem;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .subtitle {
            font-size: clamp(1.125rem, 3vw, 1.375rem);
            font-weight: 400;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 3rem;
            line-height: 1.6;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease-out 0.2s forwards;
        }

        .form-container {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease-out 0.4s forwards;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }

        .input-wrapper {
            position: relative;
            width: 100%;
            max-width: 400px;
        }

        input[type="email"] {
            width: 100%;
            padding: 1.25rem 1.5rem;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #fff;
            outline: none;
            transition: all 0.3s ease;
        }

        input[type="email"]::placeholder {
            color: rgba(255, 255, 255, 0.3);
        }

        input[type="email"]:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .submit-btn {
            width: 100%;
            max-width: 400px;
            padding: 1.25rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            background: #fff;
            color: #000;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .submit-btn.loading {
            color: transparent;
        }

        .submit-btn.loading::after {
            content: "";
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid #000;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spinner 0.8s linear infinite;
        }

        @keyframes spinner {
            to { transform: rotate(360deg); }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-message {
            color: #ff4444;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .error-message.show {
            opacity: 1;
        }

        .success-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            background: #fff;
            color: #000;
            padding: 2rem 3rem;
            border-radius: 16px;
            font-size: 1.125rem;
            font-weight: 600;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .success-message.show {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }

        .tech-accent {
            position: absolute;
            width: 1px;
            height: 60px;
            background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3), transparent);
            top: -80px;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            animation: fadeIn 1s ease-out 0.6s forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        @media (max-width: 640px) {
            .container {
                padding: 20px;
            }
            
            h1 {
                margin-bottom: 0.75rem;
            }
            
            .subtitle {
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="gradient-bg"></div>
    
    <div class="container">
        <div class="tech-accent"></div>
        <h1>AI Agents.<br>No Code.</h1>
        <p class="subtitle">
            Master the art of building AI-powered businesses without writing a single line of code.
        </p>
        
        <div class="form-container">
            <form id="emailForm">
                <div class="input-wrapper">
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        placeholder="Enter your email"
                        required
                        autocomplete="email"
                    >
                    <div class="error-message" id="errorMessage"></div>
                </div>
                <button type="submit" class="submit-btn" id="submitBtn">
                    Join the Community
                </button>
            </form>
        </div>
    </div>

    <div class="success-message" id="successMessage">
        Welcome aboard! Redirecting you to the community...
    </div>

    <script>
        const SKOOL_GROUP_URL = 'https://www.skool.com/your-group-name';
        
        const form = document.getElementById('emailForm');
        const emailInput = document.getElementById('email');
        const submitBtn = document.getElementById('submitBtn');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.classList.add('show');
            emailInput.style.borderColor = 'rgba(255, 68, 68, 0.5)';
        }

        function hideError() {
            errorMessage.classList.remove('show');
            emailInput.style.borderColor = '';
        }

        emailInput.addEventListener('input', hideError);

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = emailInput.value.trim();
            
            if (!validateEmail(email)) {
                showError('Please enter a valid email address');
                return;
            }
            
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
            
            try {
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                console.log('Email collected:', email);
                
                successMessage.classList.add('show');
                
                setTimeout(() => {
                    window.location.href = SKOOL_GROUP_URL;
                }, 2000);
                
            } catch (error) {
                console.error('Error:', error);
                showError('Something went wrong. Please try again.');
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            }
        });
        
        emailInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                form.dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>