# Repository Structure Guide

## 🎯 Overview
This guide explains how to structure multiple repositories for your AI agents project.

## 📁 Repository Structure

### 1. ai-agents-landing
**Purpose**: Marketing/landing page
**Tech Stack**: Next.js, Tailwind CSS
**Deploy To**: Vercel/Netlify
```
ai-agents-landing/
├── app/
├── components/
├── public/
├── package.json
└── README.md
```

### 2. ai-agents-frontend
**Purpose**: Main application UI
**Tech Stack**: React/Next.js, TypeScript
**Deploy To**: Vercel/AWS Amplify
```
ai-agents-frontend/
├── src/
│   ├── components/
│   ├── pages/
│   ├── services/
│   └── utils/
├── public/
├── package.json
└── README.md
```

### 3. ai-agents-backend
**Purpose**: API and business logic
**Tech Stack**: Node.js/Python, PostgreSQL
**Deploy To**: AWS/Railway/Render
```
ai-agents-backend/
├── src/
│   ├── routes/
│   ├── models/
│   ├── services/
│   └── middleware/
├── tests/
├── package.json
└── README.md
```

### 4. ai-agents-shared
**Purpose**: Shared types, utilities, constants
**Tech Stack**: TypeScript
**Published To**: npm (private registry)
```
ai-agents-shared/
├── src/
│   ├── types/
│   ├── constants/
│   └── utils/
├── package.json
└── README.md
```

## 🔧 Setup Instructions

### Step 1: Create GitHub Organization
1. Go to GitHub → Settings → Organizations
2. Create new organization (e.g., "your-company-ai")
3. Choose the free plan to start

### Step 2: Create Repositories
```bash
# For each repository:
gh repo create your-company-ai/ai-agents-landing --public
gh repo create your-company-ai/ai-agents-frontend --private
gh repo create your-company-ai/ai-agents-backend --private
gh repo create your-company-ai/ai-agents-shared --private
```

### Step 3: Set Up Base Templates

#### Landing Page Repository
```bash
cd ai-agents-landing
npm init -y
npm install next react react-dom tailwindcss
```

#### Frontend Repository
```bash
cd ai-agents-frontend
npx create-react-app . --template typescript
# or
npx create-next-app . --typescript
```

#### Backend Repository
```bash
cd ai-agents-backend
npm init -y
npm install express cors dotenv helmet
npm install -D typescript @types/node nodemon
```

## 🔗 Connecting Repositories

### Using Shared Code
```json
// In frontend/package.json
{
  "dependencies": {
    "@your-company/shared": "github:your-company-ai/ai-agents-shared#main"
  }
}
```

### Environment Variables
Each repo has its own `.env`:
```bash
# Frontend .env
REACT_APP_API_URL=https://api.yourcompany.com

# Backend .env
DATABASE_URL=postgresql://...
JWT_SECRET=...
```

## 👥 Team Access Control

### Repository Permissions
- **Landing**: Public read, team write
- **Frontend**: Private, frontend team access
- **Backend**: Private, backend team access
- **Shared**: Private, all teams read, senior devs write

### Teams Setup
1. Create teams in GitHub organization
2. Assign repositories to teams
3. Add members to appropriate teams

## 🚀 Deployment Strategy

### Separate Deployments
- **Landing**: Vercel (automatic from main branch)
- **Frontend**: Vercel/Netlify (staging + production)
- **Backend**: Railway/Render (staging + production)

### CI/CD Workflow
Each repository has its own GitHub Actions:
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Production
        run: |
          # Deployment commands
```

## 📝 Developer Onboarding

### New Developer Setup
```bash
# 1. Clone all repositories
git clone https://github.com/your-company-ai/ai-agents-landing
git clone https://github.com/your-company-ai/ai-agents-frontend
git clone https://github.com/your-company-ai/ai-agents-backend

# 2. Install dependencies
cd ai-agents-frontend && npm install
cd ../ai-agents-backend && npm install

# 3. Set up environment variables
cp .env.example .env
# Edit .env with your local settings

# 4. Start development
npm run dev
```

## 🎯 Best Practices

### 1. Consistent Naming
- Repositories: `company-service-type`
- Branches: `feature/description`, `fix/description`
- Commits: Use conventional commits

### 2. Documentation
- Each repo has its own README
- Shared documentation in company-docs repo
- API documentation in backend repo

### 3. Version Control
- Use semantic versioning
- Tag releases properly
- Maintain CHANGELOG.md

### 4. Communication Between Services
- Frontend → Backend: REST API or GraphQL
- Use environment variables for API endpoints
- Implement proper error handling

## 🔒 Security

### Repository Security
- Enable branch protection on main
- Require PR reviews
- Use GitHub Secrets for sensitive data
- Regular dependency updates

### Access Control
- Use GitHub teams for permissions
- Audit access regularly
- Remove access when people leave

## 📊 Example Implementation

### Frontend calling Backend
```typescript
// frontend/src/services/api.ts
const API_BASE = process.env.REACT_APP_API_URL;

export const fetchAgents = async () => {
  const response = await fetch(`${API_BASE}/agents`);
  return response.json();
};
```

### Backend API endpoint
```javascript
// backend/src/routes/agents.js
app.get('/agents', authenticate, async (req, res) => {
  const agents = await Agent.findAll();
  res.json(agents);
});
```

## 🚨 Common Pitfalls to Avoid

1. **Don't share node_modules** - Each repo manages its own
2. **Don't commit .env files** - Use .env.example
3. **Don't hardcode URLs** - Always use environment variables
4. **Don't mix concerns** - Keep repos focused on their purpose

## 💡 Pro Tips

1. **Use a monorepo tool** if you need shared builds (Nx, Turborepo)
2. **Set up a private npm registry** for shared packages
3. **Use GitHub Projects** for cross-repo planning
4. **Implement health checks** in your backend
5. **Use Docker** for consistent development environments

## 🎉 Next Steps

1. Create your GitHub organization
2. Set up the repositories using this structure
3. Implement basic CI/CD
4. Document your API
5. Create onboarding guides for new developers

Remember: Start simple, iterate often. You can always refactor as you grow!