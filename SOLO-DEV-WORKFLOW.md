# Solo Developer Workflow for agentised

## 🎯 Simple Repository Structure

```
agentised/
├── landingpage     → Marketing site (Next.js)
├── frontend        → Main app (React/Next.js)
├── backend         → API (Node.js/Express)
└── shared          → Shared types/utils
```

## 🚀 Quick Setup Commands

### 1. Run the setup script:
```bash
chmod +x setup-agentised.sh
./setup-agentised.sh
```

### 2. Set up each repository:

#### Landing Page (Simple Next.js)
```bash
cd ~/agentised/landingpage
npx create-next-app@latest . --typescript --tailwind --app
```

#### Frontend (Main App)
```bash
cd ~/agentised/frontend
npx create-next-app@latest . --typescript --tailwind --app
npm install axios zustand react-query
```

#### Backend (API)
```bash
cd ~/agentised/backend
npm init -y
npm install express cors dotenv
npm install -D typescript @types/node @types/express nodemon
```

## 🔗 Connecting Services

### Frontend → Backend
```typescript
// frontend/.env.local
NEXT_PUBLIC_API_URL=http://localhost:5000

// frontend/lib/api.ts
const API_URL = process.env.NEXT_PUBLIC_API_URL;

export async function fetchData() {
  const res = await fetch(`${API_URL}/api/data`);
  return res.json();
}
```

## 🚀 Deployment (Simple & Free)

### Landing Page → Vercel
```bash
cd landingpage
vercel
```

### Frontend → Vercel
```bash
cd frontend
vercel
```

### Backend → Railway/Render
```bash
cd backend
# Use Railway CLI or Render dashboard
```

## 📝 Daily Workflow

### 1. Start all services
```bash
# Terminal 1: Backend
cd ~/agentised/backend
npm run dev

# Terminal 2: Frontend
cd ~/agentised/frontend
npm run dev

# Terminal 3: Landing (if needed)
cd ~/agentised/landingpage
npm run dev
```

### 2. Make changes and commit
```bash
git add .
git commit -m "Add feature X"
git push origin main
```

### 3. Deploy when ready
- Vercel auto-deploys on push to main
- Manually deploy backend when needed

## 🎯 Pro Tips for Solo Dev

1. **Keep it simple** - Don't over-engineer
2. **Use main branch** - No need for complex git flow
3. **Deploy often** - Small, frequent updates
4. **Use .env** - Never hardcode secrets
5. **Document as you go** - Future you will thank you

## 🔒 Security Basics

### Environment Variables
```bash
# Never commit .env files
echo ".env" >> .gitignore

# Use .env.example for templates
cp .env .env.example
# Remove actual values from .env.example
```

### API Keys
- Frontend: Use `NEXT_PUBLIC_` prefix for client-side vars
- Backend: Keep all secrets server-side only

## 🆘 Common Issues

### CORS Errors
```javascript
// backend/server.js
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000'
}));
```

### Port Conflicts
- Landing: 3000
- Frontend: 3001  
- Backend: 5000

### Deployment Issues
- Check environment variables
- Verify build commands
- Check logs in deployment platform

## 📈 When to Add Complexity

Only add when you need it:
- **GitHub Actions**: When manual deploy gets tedious
- **Testing**: When bugs become frequent
- **TypeScript strict**: When type errors increase
- **Microservices**: When backend gets too large
- **Monorepo**: When sharing code gets complex

Start simple, grow as needed! 🚀