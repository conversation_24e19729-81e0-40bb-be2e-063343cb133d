# AI Agents Landing Page

Ultra-minimal landing page for AI Agents No-Code course community, inspired by xAI and Apple design principles.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create `.env.local` file from the example:
```bash
cp env.example .env.local
```

3. Update `.env.local` with your values:
- `NEXT_PUBLIC_SKOOL_URL`: Your Skool group URL
- Optional: Email service API keys for storing subscribers

4. Run development server:
```bash
npm run dev
```

## Features

- Ultra-minimal design inspired by xAI and Apple
- Email collection with validation
- Automatic redirect to Skool after signup
- API route ready for email service integration
- Smooth animations and transitions
- Fully responsive design
- TypeScript for type safety

## Deployment

Deploy easily to Vercel:

```bash
npm run build
npm run start
```

Or use Vercel's one-click deploy.

## Email Service Integration

The API route at `/api/subscribe` is ready for integration with:
- SendGrid
- Mailchimp  
- ConvertKit
- Custom database

Simply uncomment the relevant code in `app/api/subscribe/route.ts` and add your API keys.