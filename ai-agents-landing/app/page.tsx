'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion'

export default function Home() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [currentCapability, setCurrentCapability] = useState(0)
  const [activeFlow, setActiveFlow] = useState(0)
  const [terminalText, setTerminalText] = useState('')
  const [selectedAgent, setSelectedAgent] = useState(0)
  const [isTyping, setIsTyping] = useState(false)
  
  const containerRef = useRef<HTMLDivElement>(null)
  const terminalRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  })
  
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.5], [1, 0.8])

  const capabilities = [
    { 
      title: "Customer Support",
      flow: ["User Request", "AI Analysis", "Auto Response", "Happy Customer"],
      metric: "95% Faster Response"
    },
    { 
      title: "Sales Automation",
      flow: ["Lead Capture", "AI Qualification", "Personalized Outreach", "Closed Deal"],
      metric: "3x More Conversions"
    },
    { 
      title: "Content Creation",
      flow: ["Content Brief", "AI Generation", "Human Polish", "Published Asset"],
      metric: "10x Content Output"
    },
    { 
      title: "Data Analysis",
      flow: ["Raw Data", "AI Processing", "Insights Generated", "Decision Made"],
      metric: "Real-time Insights"
    }
  ]

  const agentExamples = [
    {
      name: "Customer Support",
      input: "I can't login to my account",
      process: [
        "> Agent: Analyzing user issue...",
        "> Agent: Checking account status...",
        "> Agent: Found: Password reset needed",
        "> Agent: Sending reset instructions..."
      ],
      output: "Reset link <NAME_EMAIL>. Issue resolved in 12 seconds."
    },
    {
      name: "Data Analysis",
      input: "Analyze Q4 sales performance",
      process: [
        "> Agent: Loading sales data...",
        "> Agent: Processing 15,423 transactions...",
        "> Agent: Identifying trends...",
        "> Agent: Generating insights..."
      ],
      output: "Q4 sales up 23% YoY. Top product: AI Suite. Key region: North America."
    },
    {
      name: "Content Creation",
      input: "Write a blog post about AI trends",
      process: [
        "> Agent: Researching latest AI developments...",
        "> Agent: Analyzing top trends...",
        "> Agent: Generating content structure...",
        "> Agent: Writing article..."
      ],
      output: "1,500-word blog post created: '5 AI Trends Reshaping Business in 2025'"
    }
  ]

  // Rotate through capabilities
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentCapability((prev) => (prev + 1) % capabilities.length)
      setActiveFlow(0)
    }, 5000)
    return () => clearInterval(interval)
  }, [capabilities.length])

  // Animate flow
  useEffect(() => {
    if (activeFlow < 3) {
      const timeout = setTimeout(() => {
        setActiveFlow(prev => prev + 1)
      }, 500)
      return () => clearTimeout(timeout)
    }
  }, [activeFlow, currentCapability])

  // Typewriter effect
  const typewriterEffect = async (text: string, startIndex: number = 0) => {
    setIsTyping(true)
    let currentText = text.substring(0, startIndex)
    
    for (let i = startIndex; i < text.length; i++) {
      currentText = text.substring(0, i + 1)
      setTerminalText(currentText)
      await new Promise(resolve => setTimeout(resolve, 30))
    }
    
    setIsTyping(false)
    return currentText
  }

  // Animate terminal when agent changes
  useEffect(() => {
    let isCancelled = false
    
    const animateTerminal = async () => {
      const example = agentExamples[selectedAgent]
      
      // Clear terminal
      setTerminalText('')
      if (isCancelled) return
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // Build the full terminal text
      let fullText = `> User: "${example.input}"\n`
      
      // Type input
      if (!isCancelled) {
        await typewriterEffect(fullText)
        await new Promise(resolve => setTimeout(resolve, 500))
      }
      
      // Add and type process lines
      for (const line of example.process) {
        if (isCancelled) return
        fullText += line + '\n'
        await typewriterEffect(fullText, fullText.length - line.length - 1)
        await new Promise(resolve => setTimeout(resolve, 300))
      }
      
      if (isCancelled) return
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Add and type output
      fullText += `> Result: ${example.output}`
      if (!isCancelled) {
        await typewriterEffect(fullText, fullText.length - example.output.length - 10)
      }
    }
    
    animateTerminal()
    
    return () => {
      isCancelled = true
    }
  }, [selectedAgent])

  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    
    if (!validateEmail(email)) {
      setError('Please enter a valid email address')
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Something went wrong')
      }

      setSuccess(true)
      setEmail('')
      
      // Redirect after 3 seconds
      setTimeout(() => {
        window.location.href = 'https://discord.gg/yourdiscord'
      }, 3000)
      
    } catch (err) {
      setError('Something went wrong. Please try again.')
      setLoading(false)
    }
  }

  return (
    <main className="min-h-screen bg-black text-white relative">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-black to-gray-950" />
        <motion.div 
          animate={{ 
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{ 
            duration: 20, 
            repeat: Infinity,
            ease: "linear" 
          }}
          className="absolute -top-1/2 -left-1/2 w-[200%] h-[200%] opacity-20"
        >
          <div className="absolute inset-0 bg-gradient-radial from-white/5 via-transparent to-transparent" />
        </motion.div>
        {/* Subtle grid */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.01)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.01)_1px,transparent_1px)] bg-[size:100px_100px]" />
      </div>

      {/* Hero Section with Workflow */}
      <section ref={containerRef} className="relative min-h-screen flex items-center justify-center px-5 py-20">
        <motion.div 
          style={{ opacity, scale }}
          className="w-full max-w-6xl mx-auto"
        >
          {/* Main Heading */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="text-center mb-20"
          >
            <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold tracking-tighter mb-6">
              Build AI Agents
              <span className="block text-4xl md:text-5xl lg:text-6xl text-white/70 mt-4">
                Without Code
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-white/50 max-w-3xl mx-auto">
              Transform your business with intelligent automation. 
              No developers needed. Just drag, drop, and deploy.
            </p>
          </motion.div>

          {/* Workflow Visualization */}
          <div className="relative h-[500px] flex items-center justify-center">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentCapability}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0"
              >
                {/* Capability Title */}
                <div className="text-center mb-16">
                  <motion.h2 
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-3xl md:text-4xl font-bold text-white/90"
                  >
                    {capabilities[currentCapability].title}
                  </motion.h2>
                </div>

                {/* Flow Diagram */}
                <div className="relative flex items-center justify-between max-w-5xl mx-auto px-8">
                  {capabilities[currentCapability].flow.map((step, index) => (
                    <React.Fragment key={index}>
                      {/* Node */}
                      <motion.div
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ 
                          scale: activeFlow >= index ? 1 : 0,
                          opacity: activeFlow >= index ? 1 : 0
                        }}
                        transition={{ 
                          duration: 0.5,
                          type: "spring",
                          stiffness: 200
                        }}
                        className="relative"
                      >
                        {/* Glow Effect */}
                        <motion.div
                          animate={activeFlow === index ? {
                            scale: [1, 1.5, 1],
                            opacity: [0.3, 0.6, 0.3]
                          } : {}}
                          transition={{ 
                            duration: 2,
                            repeat: Infinity
                          }}
                          className="absolute inset-0 bg-white rounded-xl blur-xl"
                        />
                        
                        {/* Node Content */}
                        <div className="relative bg-black/80 backdrop-blur-sm border border-white/20 rounded-xl px-6 py-4 min-w-[150px] text-center">
                          <p className="text-sm font-medium text-white/90">
                            {step}
                          </p>
                        </div>
                      </motion.div>

                      {/* Connector */}
                      {index < capabilities[currentCapability].flow.length - 1 && (
                        <motion.div
                          initial={{ scaleX: 0 }}
                          animate={{ 
                            scaleX: activeFlow > index ? 1 : 0
                          }}
                          transition={{ 
                            duration: 0.3,
                            delay: 0.2
                          }}
                          className="h-px bg-gradient-to-r from-white/20 to-white/40 flex-1 mx-2 origin-left"
                        />
                      )}
                    </React.Fragment>
                  ))}
                </div>

                {/* Metric Display */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ 
                    opacity: activeFlow === 3 ? 1 : 0,
                    y: activeFlow === 3 ? 0 : 20
                  }}
                  transition={{ duration: 0.5 }}
                  className="text-center mt-16"
                >
                  <p className="text-2xl font-bold text-white/90">
                    {capabilities[currentCapability].metric}
                  </p>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Capability Indicators */}
          <div className="flex justify-center gap-2 mt-12">
            {capabilities.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentCapability(index)
                  setActiveFlow(0)
                }}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  currentCapability === index 
                    ? 'w-8 bg-white' 
                    : 'bg-white/30 hover:bg-white/50'
                }`}
              />
            ))}
          </div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2 }}
            className="absolute bottom-8 left-1/2 -translate-x-1/2"
          >
            <svg className="w-6 h-6 text-white/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </motion.div>
        </motion.div>
      </section>

      {/* How AI Agents Work Section */}
      <section className="relative min-h-screen flex items-center justify-center px-5 py-20">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="w-full max-w-7xl mx-auto"
        >
          {/* Section Title */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-5xl md:text-6xl font-bold mb-6">
              How AI Agents Work
            </h2>
            <p className="text-xl text-white/50 max-w-3xl mx-auto">
              Watch AI agents process requests in real-time. No code required.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* Terminal Simulation */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              {/* Terminal Window */}
              <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden">
                {/* Terminal Header */}
                <div className="flex items-center gap-2 px-4 py-3 border-b border-white/10">
                  <div className="flex gap-2">
                    <div className="w-3 h-3 rounded-full bg-white/20"></div>
                    <div className="w-3 h-3 rounded-full bg-white/20"></div>
                    <div className="w-3 h-3 rounded-full bg-white/20"></div>
                  </div>
                  <span className="text-xs text-white/40 ml-auto">AI Agent Terminal</span>
                </div>
                
                {/* Terminal Content */}
                <div ref={terminalRef} className="p-6 font-mono text-sm min-h-[400px]">
                  <pre className="text-white/80 whitespace-pre-wrap">
                    {terminalText}
                    {isTyping && <span className="inline-block w-2 h-4 bg-white/60 animate-pulse ml-1" />}
                  </pre>
                </div>
              </div>

              {/* Agent Selector Tabs */}
              <div className="mt-6 flex gap-2">
                {agentExamples.map((agent, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedAgent(index)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                      selectedAgent === index
                        ? 'bg-white text-black'
                        : 'bg-white/5 text-white/60 hover:bg-white/10'
                    }`}
                  >
                    {agent.name}
                  </button>
                ))}
              </div>
            </motion.div>

            {/* Architecture Flow Diagram */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="relative"
            >
              {/* Flow Diagram */}
              <div className="space-y-8 relative">
                {/* Animated Flow Line */}
                <div className="absolute top-0 left-1/2 -translate-x-1/2 w-0.5 h-full pointer-events-none">
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent"
                    animate={{
                      y: ['-100%', '200%']
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10" />
                </div>
                
                {/* User Input */}
                <motion.div
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="relative z-10"
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0 }}
                  viewport={{ once: true }}
                >
                  <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group">
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    
                    {/* Animated border glow */}
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      style={{
                        background: `linear-gradient(45deg, transparent, white, transparent)`,
                        backgroundSize: '200% 200%',
                      }}
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: 0
                      }}
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 0.1 }}
                      viewport={{ once: true }}
                    />
                    
                    <div className="flex items-center gap-4 relative z-10">
                      <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center">
                        <svg className="w-6 h-6 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1 text-white/90">User Input</h3>
                        <p className="text-sm text-white/50">Natural language request</p>
                      </div>
                    </div>
                    
                    {/* Step indicator */}
                    <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center">
                      <span className="text-xs text-white/30">1</span>
                    </div>
                  </div>
                </motion.div>

                {/* AI Processing */}
                <motion.div
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="relative z-10"
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group">
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    
                    {/* Animated border glow */}
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      style={{
                        background: `linear-gradient(45deg, transparent, white, transparent)`,
                        backgroundSize: '200% 200%',
                      }}
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: 0.5
                      }}
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 0.1 }}
                      viewport={{ once: true }}
                    />
                    
                    <div className="flex items-center gap-4 relative z-10">
                      <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center relative">
                        <svg className="w-6 h-6 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        {/* Processing Indicator */}
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                          className="absolute inset-0 rounded-lg"
                        >
                          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1 h-1 bg-white/60 rounded-full" />
                        </motion.div>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1 text-white/90">AI Processing</h3>
                        <p className="text-sm text-white/50">Understanding intent</p>
                      </div>
                    </div>
                    
                    {/* Step indicator */}
                    <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center">
                      <span className="text-xs text-white/30">2</span>
                    </div>
                  </div>
                </motion.div>

                {/* Actions */}
                <motion.div
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="relative z-10"
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group">
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    
                    {/* Animated border glow */}
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      style={{
                        background: `linear-gradient(45deg, transparent, white, transparent)`,
                        backgroundSize: '200% 200%',
                      }}
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: 1
                      }}
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 0.1 }}
                      viewport={{ once: true }}
                    />
                    
                    <div className="flex items-center gap-4 relative z-10">
                      <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center">
                        <svg className="w-6 h-6 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1 text-white/90">Actions</h3>
                        <p className="text-sm text-white/50">Automated execution</p>
                      </div>
                    </div>
                    
                    {/* Step indicator */}
                    <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center">
                      <span className="text-xs text-white/30">3</span>
                    </div>
                  </div>
                </motion.div>

                {/* Results */}
                <motion.div
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="relative z-10"
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group">
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    
                    {/* Animated border glow */}
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      style={{
                        background: `linear-gradient(45deg, transparent, white, transparent)`,
                        backgroundSize: '200% 200%',
                      }}
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: 1.5
                      }}
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 0.1 }}
                      viewport={{ once: true }}
                    />
                    
                    <div className="flex items-center gap-4 relative z-10">
                      <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center">
                        <svg className="w-6 h-6 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1 text-white/90">Results</h3>
                        <p className="text-sm text-white/50">Value delivered</p>
                      </div>
                    </div>
                    
                    {/* Step indicator */}
                    <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center">
                      <span className="text-xs text-white/30">4</span>
                    </div>
                  </div>
                </motion.div>
              </div>
              
              {/* Key Metrics */}
              <div className="grid grid-cols-3 gap-4 mt-12">
                <motion.div
                  whileHover={{ y: -2 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-4 text-center"
                >
                  <p className="text-2xl font-bold text-white/80">100ms</p>
                  <p className="text-xs text-white/40 mt-1">Response Time</p>
                </motion.div>
                <motion.div
                  whileHover={{ y: -2 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-4 text-center"
                >
                  <p className="text-2xl font-bold text-white/80">∞</p>
                  <p className="text-xs text-white/40 mt-1">Scale Limit</p>
                </motion.div>
                <motion.div
                  whileHover={{ y: -2 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-4 text-center"
                >
                  <p className="text-2xl font-bold text-white/80">0</p>
                  <p className="text-xs text-white/40 mt-1">Code Required</p>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Waitlist Section */}
      <section className="relative min-h-screen flex items-center justify-center px-5 py-20">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="w-full max-w-xl mx-auto text-center"
        >
          {/* Stats */}
          <div className="flex items-center justify-center gap-12 mb-16">
            <motion.div
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <p className="text-4xl font-bold text-white/90">2,847</p>
              <p className="text-sm text-white/40">Founders Building</p>
            </motion.div>
            <div className="w-px h-12 bg-white/10" />
            <motion.div
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <p className="text-4xl font-bold text-white/90">$3.2M</p>
              <p className="text-sm text-white/40">Revenue Generated</p>
            </motion.div>
          </div>

          {/* Join Form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Join the Revolution
            </h2>
            <p className="text-xl text-white/50 mb-12">
              Get early access to the future of business automation
            </p>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="relative">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value)
                    setError('')
                  }}
                  placeholder="Enter your email"
                  className="w-full px-6 py-4 text-lg bg-white/[0.03] border border-white/10 rounded-xl text-white outline-none transition-all duration-300 placeholder:text-white/30 hover:bg-white/[0.05] focus:bg-white/[0.07] focus:border-white/20"
                  required
                />
                {error && (
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="absolute -bottom-6 left-0 text-red-500 text-sm"
                  >
                    {error}
                  </motion.p>
                )}
              </div>

              <button
                type="submit"
                disabled={loading || success}
                className="w-full px-8 py-4 text-lg font-semibold bg-white text-black rounded-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(255,255,255,0.2)] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group"
              >
                <span className="relative z-10">
                  {loading ? 'Joining...' : success ? 'Welcome aboard!' : 'Get Early Access'}
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
              </button>
            </form>
          </motion.div>

          {/* Features */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            viewport={{ once: true }}
            className="grid grid-cols-3 gap-8 mt-20"
          >
            {[
              { 
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ), 
                title: "Instant Deploy" 
              },
              { 
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                  </svg>
                ), 
                title: "Infinite Scale" 
              },
              { 
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                ), 
                title: "Zero Code" 
              }
            ].map((feature, i) => (
              <motion.div 
                key={i} 
                className="text-center"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="inline-flex p-3 rounded-xl bg-white/[0.03] text-white/40 mb-3 group-hover:text-white/60 transition-colors">
                  {feature.icon}
                </div>
                <p className="text-sm text-white/50">{feature.title}</p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </section>

      {/* Success Overlay */}
      {success && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/90 backdrop-blur-md flex items-center justify-center z-50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="bg-white text-black px-8 py-6 rounded-2xl font-semibold text-lg"
          >
            Welcome aboard! Redirecting you to the community...
          </motion.div>
        </motion.div>
      )}
    </main>
  )
}