# AI Agents Landing Page Requirements
# Last Updated: 2025-07-07

## Node.js Requirements
node: 20.x || 22.x || 23.x
npm: 10.x

## Core Dependencies
next: 15.3.5
react: 19.1.0
react-dom: 19.1.0
framer-motion: ^12.23.0
typescript: ^5.8.3

## Dev Dependencies
@types/node: ^24.0.10
@types/react: ^19.1.8
@types/react-dom: ^19.1.6
autoprefixer: ^10.4.21
postcss: ^8.5.6
tailwindcss: ^3.4.17

## Environment Variables Required
NEXT_PUBLIC_SKOOL_URL=<your_skool_url>

## Optional Environment Variables
SENDGRID_API_KEY=<optional>
MAILCHIMP_API_KEY=<optional>
CONVERTKIT_API_KEY=<optional>
DATABASE_URL=<optional>

## Installation Steps
1. Ensure Node.js 20.x or higher is installed
2. Clone the repository
3. Run: npm install
4. Copy env.example to .env.local
5. Run: npm run dev

## Known Working Configurations
- macOS 14.x with Node 20.x
- Ubuntu 22.04 with Node 20.x
- Windows 11 with Node 20.x (WSL2)