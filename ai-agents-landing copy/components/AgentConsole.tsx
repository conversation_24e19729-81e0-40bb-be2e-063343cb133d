'use client';

import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface ConsoleLine {
  type: 'command' | 'output' | 'error' | 'success' | 'thinking';
  content: string;
  timestamp: Date;
}

interface AgentConsoleProps {
  lines: ConsoleLine[];
  isBuilding: boolean;
}

export default function AgentConsole({ lines, isBuilding }: AgentConsoleProps) {
  const consoleEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    consoleEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [lines]);

  const getLineStyle = (type: ConsoleLine['type']) => {
    switch (type) {
      case 'command':
        return 'text-blue-400';
      case 'output':
        return 'text-white/80';
      case 'error':
        return 'text-red-400';
      case 'success':
        return 'text-green-400';
      case 'thinking':
        return 'text-yellow-400';
      default:
        return 'text-white/60';
    }
  };

  const getLinePrefix = (type: ConsoleLine['type']) => {
    switch (type) {
      case 'command':
        return '$ ';
      case 'error':
        return '✗ ';
      case 'success':
        return '✓ ';
      case 'thinking':
        return '◐ ';
      default:
        return '  ';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-black/90 border border-white/20 rounded-lg overflow-hidden"
    >
      {/* Console Header */}
      <div className="bg-white/5 border-b border-white/10 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full" />
          <div className="w-3 h-3 bg-yellow-500 rounded-full" />
          <div className="w-3 h-3 bg-green-500 rounded-full" />
        </div>
        <span className="text-xs text-white/40 font-mono">Agent Console</span>
      </div>

      {/* Console Content */}
      <div className="p-4 h-96 overflow-y-auto font-mono text-sm">
        {lines.map((line, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05 }}
            className={`mb-2 ${getLineStyle(line.type)}`}
          >
            <span className="opacity-60">{getLinePrefix(line.type)}</span>
            {line.content}
          </motion.div>
        ))}

        {isBuilding && (
          <motion.div
            animate={{ opacity: [0.4, 1, 0.4] }}
            transition={{ duration: 1.5, repeat: Infinity }}
            className="text-white/40"
          >
            <span className="opacity-60">◐ </span>
            Processing...
          </motion.div>
        )}

        <div ref={consoleEndRef} />
      </div>
    </motion.div>
  );
}