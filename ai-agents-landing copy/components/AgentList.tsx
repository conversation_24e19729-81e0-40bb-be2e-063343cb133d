'use client';

import { motion } from 'framer-motion';

interface Agent {
  id: string;
  name: string;
  template: string;
  status: 'active' | 'inactive' | 'building';
  createdAt: Date;
  metrics?: {
    [key: string]: string | number;
  };
}

interface AgentListProps {
  agents: Agent[];
  onUpdate: () => void;
}

const templateIcons: { [key: string]: string } = {
  'customer-support': '💬',
  'sales-automation': '📈',
  'content-creation': '✍️',
  'data-analysis': '📊'
};

export default function AgentList({ agents, onUpdate }: AgentListProps) {
  const handleToggleStatus = async (agentId: string, currentStatus: string) => {
    // In production, this would call an API to update agent status
    console.log('Toggling agent status:', agentId);
    onUpdate();
  };

  const handleDeleteAgent = async (agentId: string) => {
    if (confirm('Are you sure you want to delete this agent?')) {
      try {
        await fetch(`/api/agent/${agentId}`, {
          method: 'DELETE'
        });
        onUpdate();
      } catch (error) {
        console.error('Error deleting agent:', error);
      }
    }
  };

  if (agents.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-white/60">No agents created yet. Build your first agent to get started!</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {agents.map((agent) => (
        <motion.div
          key={agent.id}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white/5 border border-white/20 rounded-lg p-6 hover:border-white/40 transition-colors"
        >
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{templateIcons[agent.template] || '🤖'}</span>
              <div>
                <h3 className="font-semibold">{agent.name}</h3>
                <p className="text-sm text-white/60 capitalize">{agent.template.replace('-', ' ')}</p>
              </div>
            </div>
            <div className={`px-2 py-1 rounded text-xs ${
              agent.status === 'active' 
                ? 'bg-green-500/20 text-green-400' 
                : 'bg-gray-500/20 text-gray-400'
            }`}>
              {agent.status}
            </div>
          </div>

          {agent.metrics && (
            <div className="space-y-2 mb-4">
              {Object.entries(agent.metrics).map(([key, value]) => (
                <div key={key} className="flex justify-between text-sm">
                  <span className="text-white/60 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                  <span className="font-medium">{value}</span>
                </div>
              ))}
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-white/60">
            <span>Created {new Date(agent.createdAt).toLocaleDateString()}</span>
          </div>

          <div className="mt-4 flex space-x-2">
            <button
              onClick={() => handleToggleStatus(agent.id, agent.status)}
              className="flex-1 px-3 py-2 bg-white/10 rounded hover:bg-white/20 transition-colors text-sm"
            >
              {agent.status === 'active' ? 'Pause' : 'Activate'}
            </button>
            <button
              onClick={() => handleDeleteAgent(agent.id)}
              className="px-3 py-2 bg-red-500/20 text-red-400 rounded hover:bg-red-500/30 transition-colors text-sm"
            >
              Delete
            </button>
          </div>
        </motion.div>
      ))}
    </div>
  );
}