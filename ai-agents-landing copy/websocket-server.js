import { createServer } from 'http';
import { Server } from 'socket.io';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const httpServer = createServer();
const io = new Server(httpServer, {
  cors: {
    origin: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    methods: ['GET', 'POST']
  }
});

const activeSessions = new Map();

io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  socket.on('start-agent', async (data) => {
    const { sessionId, prompt, template } = data;
    
    try {
      console.log('Starting agent session:', sessionId);
      
      // Path to agentcli - adjust based on your setup
      const agentCliPath = path.join(__dirname, '..', 'agentcli', 'packages', 'cli', 'dist', 'gemini.js');
      
      // Build the full prompt with template context
      const fullPrompt = getTemplatePrompt(template, prompt);
      
      // Spawn agentcli process
      const agentProcess = spawn('node', [agentCliPath, fullPrompt], {
        env: {
          ...process.env,
          GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
        }
      });

      // Store the process
      activeSessions.set(sessionId, agentProcess);

      // Initial progress
      socket.emit('build-progress', {
        sessionId,
        status: 'initializing',
        progress: 10,
        currentStep: 'Starting AI agent...',
        logs: ['Initializing agent builder...'],
        timestamp: new Date()
      });

      // Handle stdout (agent responses)
      agentProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log('Agent output:', output);
        
        // Parse different types of output
        if (output.includes('Thinking') || output.includes('analyzing')) {
          socket.emit('agent-thinking', {
            sessionId,
            thought: output.trim(),
            confidence: 0.8,
            timestamp: new Date()
          });
          
          socket.emit('build-progress', {
            sessionId,
            status: 'building',
            progress: 30,
            currentStep: 'AI is thinking...',
            logs: [output],
            timestamp: new Date()
          });
        } else if (output.includes('Executing') || output.includes('Running')) {
          const toolMatch = output.match(/(?:Executing|Running):?\s*(.+)/i);
          const toolName = toolMatch ? toolMatch[1].trim() : 'Tool';
          
          socket.emit('tool-execution', {
            sessionId,
            toolName,
            status: 'running',
            output: output,
            timestamp: new Date()
          });
          
          socket.emit('build-progress', {
            sessionId,
            status: 'building',
            progress: 60,
            currentStep: `Executing ${toolName}...`,
            logs: [output],
            timestamp: new Date()
          });
        } else {
          socket.emit('agent-output', {
            sessionId,
            output,
            timestamp: new Date()
          });
          
          socket.emit('build-progress', {
            sessionId,
            status: 'building',
            progress: 80,
            currentStep: 'Processing response...',
            logs: [output],
            timestamp: new Date()
          });
        }
      });

      // Handle stderr (errors)
      agentProcess.stderr.on('data', (data) => {
        const error = data.toString();
        console.error('Agent error:', error);
        
        socket.emit('agent-error', {
          sessionId,
          error,
          timestamp: new Date()
        });
      });

      // Handle process exit
      agentProcess.on('close', (code) => {
        console.log('Agent process closed with code:', code);
        activeSessions.delete(sessionId);
        
        if (code === 0) {
          socket.emit('build-progress', {
            sessionId,
            status: 'completed',
            progress: 100,
            currentStep: 'Agent created successfully!',
            logs: ['Build completed'],
            timestamp: new Date()
          });
        }
        
        socket.emit('agent-complete', {
          sessionId,
          exitCode: code,
          timestamp: new Date()
        });
      });

    } catch (error) {
      console.error('Error starting agent:', error);
      socket.emit('agent-error', {
        sessionId,
        error: error.message,
        timestamp: new Date()
      });
    }
  });

  socket.on('send-message', (data) => {
    const { sessionId, message } = data;
    const process = activeSessions.get(sessionId);
    
    if (process && !process.killed) {
      process.stdin.write(message + '\n');
    } else {
      socket.emit('agent-error', {
        sessionId,
        error: 'No active session found',
        timestamp: new Date()
      });
    }
  });

  socket.on('stop-agent', (data) => {
    const { sessionId } = data;
    const process = activeSessions.get(sessionId);
    
    if (process) {
      process.kill();
      activeSessions.delete(sessionId);
      socket.emit('agent-stopped', {
        sessionId,
        timestamp: new Date()
      });
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

function getTemplatePrompt(template, userPrompt) {
  const templates = {
    'customer-support': `You are a professional customer support AI agent. Your role is to:
- Answer customer questions politely and accurately
- Resolve issues efficiently
- Escalate complex problems when needed
- Maintain a friendly, helpful tone

User Request: ${userPrompt}`,
    
    'sales-automation': `You are an AI sales assistant. Your responsibilities include:
- Qualifying leads based on specific criteria
- Scheduling meetings and follow-ups
- Sending personalized outreach emails
- Updating CRM with interaction data

User Request: ${userPrompt}`,
    
    'content-creation': `You are an AI content creation specialist. Your tasks include:
- Writing engaging blog posts and articles
- Creating social media content
- Optimizing content for SEO
- Maintaining brand voice and style

User Request: ${userPrompt}`,
    
    'data-analysis': `You are an AI data analyst. Your role involves:
- Analyzing business metrics and KPIs
- Creating visualizations and reports
- Identifying trends and patterns
- Providing actionable insights

User Request: ${userPrompt}`
  };
  
  return templates[template] || userPrompt;
}

const PORT = process.env.WEBSOCKET_PORT || 3001;
httpServer.listen(PORT, () => {
  console.log(`WebSocket server running on port ${PORT}`);
});

// Clean up on exit
process.on('SIGTERM', () => {
  activeSessions.forEach(process => process.kill());
  httpServer.close();
});