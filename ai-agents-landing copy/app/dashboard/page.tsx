'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import AgentBuilder from '@/components/AgentBuilder';
import AgentList from '@/components/AgentList';
import AgentMetrics from '@/components/AgentMetrics';

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState<'builder' | 'agents' | 'metrics'>('builder');
  const [userAgents, setUserAgents] = useState([]);

  useEffect(() => {
    fetchUserAgents();
  }, []);

  const fetchUserAgents = async () => {
    try {
      const response = await fetch('/api/agent', {
        headers: {
          'x-user-id': 'demo-user' // In production, get from auth
        }
      });
      const data = await response.json();
      setUserAgents(data.agents);
    } catch (error) {
      console.error('Error fetching agents:', error);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-3">
              <span className="text-xl font-bold">AI Agents</span>
            </Link>
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab('builder')}
                className={`px-3 py-2 text-sm font-medium transition-colors ${
                  activeTab === 'builder' 
                    ? 'text-white border-b-2 border-white' 
                    : 'text-white/60 hover:text-white'
                }`}
              >
                Agent Builder
              </button>
              <button
                onClick={() => setActiveTab('agents')}
                className={`px-3 py-2 text-sm font-medium transition-colors ${
                  activeTab === 'agents' 
                    ? 'text-white border-b-2 border-white' 
                    : 'text-white/60 hover:text-white'
                }`}
              >
                My Agents
              </button>
              <button
                onClick={() => setActiveTab('metrics')}
                className={`px-3 py-2 text-sm font-medium transition-colors ${
                  activeTab === 'metrics' 
                    ? 'text-white border-b-2 border-white' 
                    : 'text-white/60 hover:text-white'
                }`}
              >
                Analytics
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'builder' && (
            <div>
              <h1 className="text-3xl font-bold mb-8">Build Your AI Agent</h1>
              <AgentBuilder onAgentCreated={fetchUserAgents} />
            </div>
          )}

          {activeTab === 'agents' && (
            <div>
              <h1 className="text-3xl font-bold mb-8">Your AI Agents</h1>
              <AgentList agents={userAgents} onUpdate={fetchUserAgents} />
            </div>
          )}

          {activeTab === 'metrics' && (
            <div>
              <h1 className="text-3xl font-bold mb-8">Agent Analytics</h1>
              <AgentMetrics agents={userAgents} />
            </div>
          )}
        </motion.div>
      </main>
    </div>
  );
}