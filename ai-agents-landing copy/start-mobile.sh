#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting AI Agents Landing Page with Mobile Access${NC}"
echo ""

# Kill any existing processes
echo -e "${YELLOW}Cleaning up existing processes...${NC}"
killall ngrok 2>/dev/null
lsof -ti:3000 | xargs kill -9 2>/dev/null
lsof -ti:3001 | xargs kill -9 2>/dev/null

# Start Next.js in background
echo -e "${GREEN}Starting Next.js development server...${NC}"
npm run dev &
NEXTJS_PID=$!

# Wait for Next.js to start
echo -e "${YELLOW}Waiting for Next.js to start...${NC}"
sleep 5

# Check which port Next.js is using
if lsof -i:3000 >/dev/null 2>&1; then
    PORT=3000
elif lsof -i:3001 >/dev/null 2>&1; then
    PORT=3001
else
    echo -e "${YELLOW}Next.js might still be starting. Assuming port 3000...${NC}"
    PORT=3000
fi

# Start ngrok
echo -e "${GREEN}Starting ngrok tunnel on port $PORT...${NC}"
ngrok http $PORT > /tmp/ngrok.log 2>&1 &
NGROK_PID=$!

# Wait for ngrok to start
sleep 3

# Get ngrok URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | grep -o '"public_url":"https://[^"]*' | cut -d'"' -f4 | head -1)

if [ -z "$NGROK_URL" ]; then
    echo -e "${YELLOW}Waiting for ngrok to initialize...${NC}"
    sleep 2
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | grep -o '"public_url":"https://[^"]*' | cut -d'"' -f4 | head -1)
fi

# Display access information
echo ""
echo -e "${GREEN}✅ Everything is running!${NC}"
echo ""
echo -e "${BLUE}📱 Mobile Access:${NC}"
echo -e "   Public URL: ${GREEN}$NGROK_URL${NC}"
echo ""
echo -e "${BLUE}💻 Local Access:${NC}"
echo -e "   Local URL: ${GREEN}http://localhost:$PORT${NC}"
echo -e "   Network URL: ${GREEN}http://$(ipconfig getifaddr en0):$PORT${NC}"
echo ""
echo -e "${BLUE}📊 ngrok Dashboard:${NC}"
echo -e "   ${GREEN}http://localhost:4040${NC}"
echo ""
echo -e "${YELLOW}Press Ctrl+C to stop all services${NC}"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo -e "${YELLOW}Shutting down...${NC}"
    kill $NEXTJS_PID 2>/dev/null
    kill $NGROK_PID 2>/dev/null
    killall ngrok 2>/dev/null
    echo -e "${GREEN}✅ All services stopped${NC}"
    exit 0
}

# Set up trap to cleanup on Ctrl+C
trap cleanup INT

# Keep script running
wait $NEXTJS_PID