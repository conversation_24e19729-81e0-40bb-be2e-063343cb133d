import { Server } from 'socket.io';
import { NextRequest } from 'next/server';
import { spawn, ChildProcess } from 'child_process';
import path from 'path';

let io: Server | null = null;
const activeSessions = new Map<string, ChildProcess>();

export async function GET(request: NextRequest) {
  if (!io) {
    // @ts-ignore
    io = new Server({
      cors: {
        origin: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
        methods: ['GET', 'POST']
      }
    });

    io.on('connection', (socket) => {
      console.log('Client connected:', socket.id);

      socket.on('start-agent', async (data) => {
        const { sessionId, prompt, template } = data;
        
        try {
          // Build the agentcli command
          const agentCliPath = path.join(process.cwd(), '..', 'agentcli', 'packages', 'cli', 'dist', 'gemini.js');
          
          // Spawn agentcli process
          const agentProcess = spawn('node', [agentCliPath, '--no-interactive'], {
            env: {
              ...process.env,
              GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
            }
          });

          // Store the process
          activeSessions.set(sessionId, agentProcess);

          // Send initial prompt
          agentProcess.stdin.write(prompt + '\n');

          // Handle stdout (agent responses)
          agentProcess.stdout.on('data', (data) => {
            const output = data.toString();
            
            // Parse output to detect different types of events
            if (output.includes('Thinking...')) {
              socket.emit('agent-thinking', {
                sessionId,
                thought: output,
                timestamp: new Date()
              });
            } else if (output.includes('Executing:')) {
              socket.emit('tool-execution', {
                sessionId,
                tool: output.match(/Executing: (.*)/)?.[1] || 'unknown',
                status: 'running',
                timestamp: new Date()
              });
            } else {
              socket.emit('agent-output', {
                sessionId,
                output,
                timestamp: new Date()
              });
            }

            // Emit progress updates
            socket.emit('build-progress', {
              sessionId,
              status: 'building',
              progress: 50,
              currentStep: 'Processing with AI agent...',
              logs: [output],
              timestamp: new Date()
            });
          });

          // Handle stderr (errors)
          agentProcess.stderr.on('data', (data) => {
            socket.emit('agent-error', {
              sessionId,
              error: data.toString(),
              timestamp: new Date()
            });
          });

          // Handle process exit
          agentProcess.on('close', (code) => {
            activeSessions.delete(sessionId);
            socket.emit('agent-complete', {
              sessionId,
              exitCode: code,
              timestamp: new Date()
            });
          });

        } catch (error) {
          socket.emit('agent-error', {
            sessionId,
            error: error.message,
            timestamp: new Date()
          });
        }
      });

      socket.on('send-message', (data) => {
        const { sessionId, message } = data;
        const process = activeSessions.get(sessionId);
        
        if (process && !process.killed) {
          process.stdin.write(message + '\n');
        } else {
          socket.emit('agent-error', {
            sessionId,
            error: 'No active session found',
            timestamp: new Date()
          });
        }
      });

      socket.on('stop-agent', (data) => {
        const { sessionId } = data;
        const process = activeSessions.get(sessionId);
        
        if (process) {
          process.kill();
          activeSessions.delete(sessionId);
          socket.emit('agent-stopped', {
            sessionId,
            timestamp: new Date()
          });
        }
      });

      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
        // Clean up any active sessions for this socket
        // In production, you'd track which sessions belong to which socket
      });
    });

    // Start the Socket.IO server on a different port
    const port = parseInt(process.env.WEBSOCKET_PORT || '3001');
    io.listen(port);
    console.log(`WebSocket server started on port ${port}`);
  }

  return new Response('WebSocket server is running', { status: 200 });
}

// Clean up on server shutdown
if (typeof window === 'undefined') {
  process.on('SIGTERM', () => {
    if (io) {
      io.close();
    }
    activeSessions.forEach(process => process.kill());
  });
}