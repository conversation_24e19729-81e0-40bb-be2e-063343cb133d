import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs/promises';

// Business agent templates with specialized prompts
const AGENT_TEMPLATES = {
  'customer-support': {
    systemPrompt: `You are a professional customer support AI agent. Your role is to:
- Answer customer questions politely and accurately
- Resolve issues efficiently
- Escalate complex problems when needed
- Maintain a friendly, helpful tone
- Track common issues for reporting

Always prioritize customer satisfaction and provide clear, actionable solutions.`,
    tools: ['web-search', 'knowledge-base', 'ticket-system']
  },
  'sales-automation': {
    systemPrompt: `You are an AI sales assistant. Your responsibilities include:
- Qualifying leads based on specific criteria
- Scheduling meetings and follow-ups
- Sending personalized outreach emails
- Updating CRM with interaction data
- Providing sales insights and recommendations

Focus on building relationships and moving prospects through the sales funnel.`,
    tools: ['email', 'calendar', 'crm-integration', 'web-search']
  },
  'content-creation': {
    systemPrompt: `You are an AI content creation specialist. Your tasks include:
- Writing engaging blog posts and articles
- Creating social media content
- Optimizing content for SEO
- Maintaining brand voice and style
- Generating content calendars

Ensure all content is original, valuable, and aligned with the brand's messaging.`,
    tools: ['write-file', 'web-search', 'image-generation', 'seo-analysis']
  },
  'data-analysis': {
    systemPrompt: `You are an AI data analyst. Your role involves:
- Analyzing business metrics and KPIs
- Creating visualizations and reports
- Identifying trends and patterns
- Providing actionable insights
- Automating recurring reports

Focus on delivering clear, data-driven recommendations for business improvement.`,
    tools: ['data-query', 'visualization', 'report-generation', 'web-search']
  }
};

export async function POST(request: NextRequest) {
  try {
    const { sessionId, prompt, template, context } = await request.json();

    if (!sessionId || !prompt || !template) {
      return NextResponse.json(
        { error: 'Session ID, prompt, and template are required' },
        { status: 400 }
      );
    }

    const agentTemplate = AGENT_TEMPLATES[template as keyof typeof AGENT_TEMPLATES];
    if (!agentTemplate) {
      return NextResponse.json(
        { error: 'Invalid template' },
        { status: 400 }
      );
    }

    // Construct the full prompt with template context
    const fullPrompt = `${agentTemplate.systemPrompt}\n\nUser Request: ${prompt}\n\nContext: ${context || 'No additional context provided'}`;

    // Path to agentcli
    const agentCliPath = path.join(process.cwd(), '..', 'agentcli', 'packages', 'cli', 'dist', 'gemini.js');

    // Check if agentcli exists
    try {
      await fs.access(agentCliPath);
    } catch {
      // If built version doesn't exist, use the development command
      return NextResponse.json({
        message: 'Agent CLI not built. Please build agentcli first.',
        buildCommand: 'cd ../agentcli && npm run build'
      }, { status: 503 });
    }

    // Execute agentcli with the prompt
    const agentProcess = spawn('node', [agentCliPath, fullPrompt], {
      env: {
        ...process.env,
        // Add any necessary environment variables
        GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
      }
    });

    let output = '';
    let error = '';

    agentProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    agentProcess.stderr.on('data', (data) => {
      error += data.toString();
    });

    return new Promise<NextResponse>((resolve) => {
      agentProcess.on('close', (code) => {
        if (code !== 0) {
          resolve(NextResponse.json(
            { error: 'Agent execution failed', details: error },
            { status: 500 }
          ));
        } else {
          resolve(NextResponse.json({
            sessionId,
            output,
            template,
            status: 'completed'
          }));
        }
      });
    });

  } catch (error) {
    console.error('Error executing agent:', error);
    return NextResponse.json(
      { error: 'Failed to execute agent' },
      { status: 500 }
    );
  }
}