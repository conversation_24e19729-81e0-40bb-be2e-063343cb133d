<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      margin: 0;
      padding: 0;
      background: #00FF00; /* Green screen for easy removal */
      width: 100vw;
      height: 100vh;
      overflow: hidden;
    }
    
    /* Main flowing light */
    .light-flow {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 4px;
      height: 300px;
      background: linear-gradient(to bottom, 
        transparent 0%, 
        rgba(255,255,255,0.3) 20%,
        rgba(255,255,255,0.8) 50%,
        rgba(255,255,255,0.3) 80%,
        transparent 100%);
      filter: blur(2px);
      animation: flowDown 3s linear infinite;
    }
    
    /* Brighter center core */
    .light-core {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 2px;
      height: 200px;
      background: linear-gradient(to bottom, 
        transparent 0%, 
        rgba(255,255,255,0.6) 30%,
        white 50%,
        rgba(255,255,255,0.6) 70%,
        transparent 100%);
      animation: flowDown 3s linear infinite;
    }
    
    /* Glow effect */
    .light-glow {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 400px;
      background: radial-gradient(ellipse at center, 
        rgba(255,255,255,0.2) 0%, 
        transparent 70%);
      filter: blur(10px);
      animation: flowDown 3s linear infinite;
    }
    
    @keyframes flowDown {
      from {
        top: -400px;
      }
      to {
        top: 100vh;
      }
    }
    
    /* Multiple lights for continuous effect */
    .light-flow:nth-child(2),
    .light-core:nth-child(2),
    .light-glow:nth-child(2) {
      animation-delay: 1s;
    }
    
    .light-flow:nth-child(3),
    .light-core:nth-child(3),
    .light-glow:nth-child(3) {
      animation-delay: 2s;
    }
  </style>
</head>
<body>
  <!-- Three sets of lights for continuous flow -->
  <div class="light-glow"></div>
  <div class="light-flow"></div>
  <div class="light-core"></div>
  
  <div class="light-glow"></div>
  <div class="light-flow"></div>
  <div class="light-core"></div>
  
  <div class="light-glow"></div>
  <div class="light-flow"></div>
  <div class="light-core"></div>
</body>
</html>