'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import io, { Socket } from 'socket.io-client';

interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  example: string;
}

const AGENT_TEMPLATES: AgentTemplate[] = [
  {
    id: 'customer-support',
    name: 'Customer Support',
    description: 'Handles customer inquiries, resolves issues, and provides 24/7 support',
    icon: '💬',
    example: 'Create a customer support agent that can answer questions about our return policy and help process refunds'
  },
  {
    id: 'sales-automation',
    name: 'Sales Automation',
    description: 'Qualifies leads, schedules meetings, and manages follow-ups',
    icon: '📈',
    example: 'Build a sales agent that qualifies leads from our website and schedules demos with interested prospects'
  },
  {
    id: 'content-creation',
    name: 'Content Creation',
    description: 'Generates blog posts, social media content, and marketing materials',
    icon: '✍️',
    example: 'Create a content agent that writes weekly blog posts about AI trends in our industry'
  },
  {
    id: 'data-analysis',
    name: 'Data Analysis',
    description: 'Analyzes data, creates reports, and provides business insights',
    icon: '📊',
    example: 'Build an analytics agent that generates daily reports on website traffic and conversion rates'
  }
];

export default function AgentBuilder({ onAgentCreated }: { onAgentCreated: () => void }) {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [agentName, setAgentName] = useState('');
  const [agentPrompt, setAgentPrompt] = useState('');
  const [isBuilding, setIsBuilding] = useState(false);
  const [buildProgress, setBuildProgress] = useState(0);
  const [buildLogs, setBuildLogs] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState('');
  const [socket, setSocket] = useState<Socket | null>(null);
  const logsEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initialize WebSocket connection
    const socketInstance = io('http://localhost:3001');
    setSocket(socketInstance);

    socketInstance.on('connect', () => {
      console.log('Connected to WebSocket server');
    });

    socketInstance.on('build-progress', (data) => {
      setBuildProgress(data.progress);
      setCurrentStep(data.currentStep);
      if (data.logs.length > 0) {
        setBuildLogs(prev => [...prev, ...data.logs]);
      }
    });

    socketInstance.on('agent-output', (data) => {
      setBuildLogs(prev => [...prev, data.output]);
    });

    socketInstance.on('agent-thinking', (data) => {
      setBuildLogs(prev => [...prev, `🤔 ${data.thought}`]);
    });

    socketInstance.on('tool-execution', (data) => {
      setBuildLogs(prev => [...prev, `🔧 Executing: ${data.tool}`]);
    });

    socketInstance.on('agent-complete', (data) => {
      setIsBuilding(false);
      setBuildProgress(100);
      setCurrentStep('Agent created successfully!');
      setBuildLogs(prev => [...prev, '✅ Agent build completed!']);
      onAgentCreated();
    });

    socketInstance.on('agent-error', (data) => {
      setIsBuilding(false);
      setBuildLogs(prev => [...prev, `❌ Error: ${data.error}`]);
    });

    return () => {
      socketInstance.disconnect();
    };
  }, [onAgentCreated]);

  useEffect(() => {
    // Auto-scroll logs
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [buildLogs]);

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = AGENT_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setAgentPrompt(template.example);
    }
  };

  const startBuilding = async () => {
    if (!selectedTemplate || !agentName || !agentPrompt) {
      alert('Please fill in all fields');
      return;
    }

    setIsBuilding(true);
    setBuildProgress(0);
    setBuildLogs([]);
    setCurrentStep('Initializing agent builder...');

    const sessionId = `session_${Date.now()}`;

    // Start agent building via WebSocket
    socket?.emit('start-agent', {
      sessionId,
      prompt: agentPrompt,
      template: selectedTemplate
    });

    // Also create the agent via API
    try {
      await fetch('/api/agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: agentName,
          prompt: agentPrompt,
          template: selectedTemplate,
          userId: 'demo-user'
        })
      });
    } catch (error) {
      console.error('Error creating agent:', error);
    }
  };

  return (
    <div className="space-y-8">
      {/* Template Selection */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Choose a Template</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {AGENT_TEMPLATES.map((template) => (
            <motion.button
              key={template.id}
              onClick={() => handleTemplateSelect(template.id)}
              className={`p-4 rounded-lg border text-left transition-all ${
                selectedTemplate === template.id
                  ? 'border-white bg-white/10'
                  : 'border-white/20 hover:border-white/40'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{template.icon}</span>
                <div className="flex-1">
                  <h3 className="font-semibold">{template.name}</h3>
                  <p className="text-sm text-white/60 mt-1">{template.description}</p>
                </div>
              </div>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Agent Configuration */}
      {selectedTemplate && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          <div>
            <label className="block text-sm font-medium mb-2">Agent Name</label>
            <input
              type="text"
              value={agentName}
              onChange={(e) => setAgentName(e.target.value)}
              className="w-full px-4 py-2 bg-white/5 border border-white/20 rounded-lg focus:outline-none focus:border-white"
              placeholder="e.g., Customer Support Bot"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Agent Instructions</label>
            <textarea
              value={agentPrompt}
              onChange={(e) => setAgentPrompt(e.target.value)}
              className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg focus:outline-none focus:border-white h-32 resize-none"
              placeholder="Describe what you want your agent to do..."
            />
          </div>

          <button
            onClick={startBuilding}
            disabled={isBuilding}
            className="w-full py-3 bg-white text-black rounded-lg font-medium hover:bg-white/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isBuilding ? 'Building Agent...' : 'Build Agent'}
          </button>
        </motion.div>
      )}

      {/* Build Progress */}
      <AnimatePresence>
        {(isBuilding || buildLogs.length > 0) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-4"
          >
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>{currentStep}</span>
                <span>{buildProgress}%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <motion.div
                  className="bg-white h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${buildProgress}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>

            <div className="bg-black/50 border border-white/20 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
              {buildLogs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))}
              <div ref={logsEndRef} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}