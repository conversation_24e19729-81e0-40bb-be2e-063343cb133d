'use client';

import { motion } from 'framer-motion';

interface Agent {
  id: string;
  name: string;
  template: string;
  metrics?: {
    [key: string]: string | number;
  };
}

interface AgentMetricsProps {
  agents: Agent[];
}

export default function AgentMetrics({ agents }: AgentMetricsProps) {
  // Calculate aggregate metrics
  const totalAgents = agents.length;
  const activeAgents = agents.filter(a => a.status === 'active').length;
  
  // Mock aggregate data - in production, this would come from API
  const aggregateMetrics = {
    totalInteractions: 15420,
    avgResponseTime: '1.8s',
    satisfactionRate: '94.2%',
    tasksCompleted: 8934,
    costSavings: '$24,500',
    hoursAutomated: 1240
  };

  const chartData = [
    { day: 'Mon', interactions: 2100 },
    { day: 'Tue', interactions: 2450 },
    { day: 'Wed', interactions: 2200 },
    { day: 'Thu', interactions: 2800 },
    { day: 'Fri', interactions: 3100 },
    { day: 'Sat', interactions: 1400 },
    { day: 'Sun', interactions: 1370 }
  ];

  const maxInteractions = Math.max(...chartData.map(d => d.interactions));

  return (
    <div className="space-y-8">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white/5 border border-white/20 rounded-lg p-6"
        >
          <h3 className="text-sm text-white/60 mb-2">Total Agents</h3>
          <p className="text-3xl font-bold">{totalAgents}</p>
          <p className="text-sm text-green-400 mt-2">
            {activeAgents} active
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white/5 border border-white/20 rounded-lg p-6"
        >
          <h3 className="text-sm text-white/60 mb-2">Total Interactions</h3>
          <p className="text-3xl font-bold">{aggregateMetrics.totalInteractions.toLocaleString()}</p>
          <p className="text-sm text-white/60 mt-2">
            Avg response: {aggregateMetrics.avgResponseTime}
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white/5 border border-white/20 rounded-lg p-6"
        >
          <h3 className="text-sm text-white/60 mb-2">Cost Savings</h3>
          <p className="text-3xl font-bold">{aggregateMetrics.costSavings}</p>
          <p className="text-sm text-white/60 mt-2">
            {aggregateMetrics.hoursAutomated} hours automated
          </p>
        </motion.div>
      </div>

      {/* Weekly Activity Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white/5 border border-white/20 rounded-lg p-6"
      >
        <h3 className="text-lg font-semibold mb-6">Weekly Activity</h3>
        <div className="h-64 flex items-end justify-between space-x-2">
          {chartData.map((data, index) => (
            <div key={data.day} className="flex-1 flex flex-col items-center">
              <motion.div
                initial={{ height: 0 }}
                animate={{ height: `${(data.interactions / maxInteractions) * 100}%` }}
                transition={{ delay: 0.5 + index * 0.1, duration: 0.5 }}
                className="w-full bg-white/20 rounded-t relative group"
              >
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/80 px-2 py-1 rounded text-xs whitespace-nowrap">
                  {data.interactions}
                </div>
              </motion.div>
              <span className="text-xs text-white/60 mt-2">{data.day}</span>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white/5 border border-white/20 rounded-lg p-6"
        >
          <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Satisfaction Rate</span>
                <span>{aggregateMetrics.satisfactionRate}</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div className="bg-green-400 h-2 rounded-full" style={{ width: aggregateMetrics.satisfactionRate }} />
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Task Completion Rate</span>
                <span>87.5%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div className="bg-blue-400 h-2 rounded-full" style={{ width: '87.5%' }} />
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Automation Efficiency</span>
                <span>92.3%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div className="bg-purple-400 h-2 rounded-full" style={{ width: '92.3%' }} />
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white/5 border border-white/20 rounded-lg p-6"
        >
          <h3 className="text-lg font-semibold mb-4">Agent Performance</h3>
          <div className="space-y-3">
            {agents.slice(0, 4).map((agent) => (
              <div key={agent.id} className="flex items-center justify-between">
                <span className="text-sm">{agent.name}</span>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-white/60">
                    {agent.metrics?.messagesHandled || agent.metrics?.leadsProcessed || '0'} tasks
                  </span>
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}