'use client'

import { useEffect, useRef } from 'react'

export default function ParticleNetwork() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = window.innerWidth
    canvas.height = window.innerHeight

    const isMobile = window.innerWidth < 768

    const particles: Array<{
      x: number
      y: number
      vx: number
      vy: number
      radius: number
      opacity: number
    }> = []

    const particleCount = isMobile ? 30 : 80
    const connectionDistance = isMobile ? 80 : 120
    const mouseRadius = isMobile ? 0 : 150

    let mouseX = 0
    let mouseY = 0
    let frame = 0

    // Create particles
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        radius: Math.random() * 1.5 + 0.5,
        opacity: Math.random() * 0.5 + 0.5
      })
    }

    function drawParticle(particle: typeof particles[0]) {
      if (!ctx) return
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2)
      ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity * 0.15})`
      ctx.fill()
      
      // Glow effect (disabled on mobile for performance)
      if (!isMobile) {
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, particle.radius * 3
        )
        gradient.addColorStop(0, `rgba(255, 255, 255, ${particle.opacity * 0.1})`)
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)')
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.radius * 3, 0, Math.PI * 2)
        ctx.fillStyle = gradient
        ctx.fill()
      }
    }

    function drawConnection(p1: typeof particles[0], p2: typeof particles[0], distance: number) {
      if (!ctx) return
      const opacity = (1 - distance / connectionDistance) * 0.5
      
      // Main connection line
      ctx.beginPath()
      ctx.moveTo(p1.x, p1.y)
      ctx.lineTo(p2.x, p2.y)
      ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.15})`
      ctx.lineWidth = 0.5
      ctx.stroke()
      
      // Animated pulse along connection (disabled on mobile for performance)
      if (!isMobile) {
        const pulse = (frame % 100) / 100
        const pulseX = p1.x + (p2.x - p1.x) * pulse
        const pulseY = p1.y + (p2.y - p1.y) * pulse
        
        ctx.beginPath()
        ctx.arc(pulseX, pulseY, 1, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(255, 255, 255, ${opacity * 0.3})`
        ctx.fill()
      }
    }

    function animate() {
      if (!ctx || !canvas) return
      
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      frame++

      particles.forEach((particle, i) => {
        // Update position
        particle.x += particle.vx
        particle.y += particle.vy

        // Bounce off walls softly
        if (particle.x < 0 || particle.x > canvas.width) {
          particle.vx *= -0.9
          particle.x = Math.max(0, Math.min(canvas.width, particle.x))
        }
        if (particle.y < 0 || particle.y > canvas.height) {
          particle.vy *= -0.9
          particle.y = Math.max(0, Math.min(canvas.height, particle.y))
        }

        // Mouse interaction with smooth force
        const dx = mouseX - particle.x
        const dy = mouseY - particle.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        if (distance < mouseRadius && distance > 0) {
          const force = (mouseRadius - distance) / mouseRadius
          const forceX = (dx / distance) * force * 0.02
          const forceY = (dy / distance) * force * 0.02
          particle.vx -= forceX
          particle.vy -= forceY
        }

        // Add slight random movement
        particle.vx += (Math.random() - 0.5) * 0.01
        particle.vy += (Math.random() - 0.5) * 0.01

        // Limit velocity
        const speed = Math.sqrt(particle.vx * particle.vx + particle.vy * particle.vy)
        if (speed > 0.5) {
          particle.vx = (particle.vx / speed) * 0.5
          particle.vy = (particle.vy / speed) * 0.5
        }

        // Update opacity with breathing effect
        particle.opacity = 0.5 + Math.sin(frame * 0.02 + i) * 0.3

        drawParticle(particle)

        // Draw connections
        for (let j = i + 1; j < particles.length; j++) {
          const p2 = particles[j]
          const dx = particle.x - p2.x
          const dy = particle.y - p2.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < connectionDistance) {
            drawConnection(particle, p2, distance)
          }
        }
      })

      requestAnimationFrame(animate)
    }

    function handleMouseMove(e: MouseEvent) {
      if (!canvas) return
      const rect = canvas.getBoundingClientRect()
      mouseX = e.clientX - rect.left
      mouseY = e.clientY - rect.top
    }

    function handleResize() {
      if (!canvas) return
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }

    if (canvas) {
      canvas.addEventListener('mousemove', handleMouseMove)
    }
    window.addEventListener('resize', handleResize)

    animate()

    return () => {
      if (canvas) {
        canvas.removeEventListener('mousemove', handleMouseMove)
      }
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0"
      style={{ opacity: 0.4, pointerEvents: 'none' }}
    />
  )
}