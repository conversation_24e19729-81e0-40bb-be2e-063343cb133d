#!/bin/bash

echo "🚀 Migrating your code to agentised repositories..."
echo "=================================================="

# 1. Move landing page code
echo "📄 Moving landing page code..."
cd ~/agentised/landingpage

# Copy all files from your current landing page (excluding git)
rsync -av --exclude='.git' --exclude='node_modules' \
  "/Applications/AI Project /course/ai-agents-landing/" .

# Commit and push
git add -A
git commit -m "Migrate landing page from local project"
git push origin main

echo "✅ Landing page migrated!"

# 2. Set up frontend with your agent interface
echo "💻 Setting up frontend..."
cd ~/agentised/frontend

# Initialize Next.js with TypeScript
cat > package.json << 'EOF'
{
  "name": "agentised-frontend",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --port 3001",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  }
}
EOF

# Install dependencies
npm install next@latest react@latest react-dom@latest typescript @types/react @types/node
npm install axios zustand @tanstack/react-query tailwindcss autoprefixer postcss

# Create basic structure
mkdir -p app/api app/components app/lib
echo "# Frontend is ready!" > README.md

git add -A
git commit -m "Initialize frontend with Next.js and dependencies"
git push origin main

echo "✅ Frontend initialized!"

# 3. Set up backend
echo "🔧 Setting up backend..."
cd ~/agentised/backend

# Create package.json
cat > package.json << 'EOF'
{
  "name": "agentised-backend",
  "version": "1.0.0",
  "description": "Backend API for agentised platform",
  "main": "dist/index.js",
  "scripts": {
    "dev": "nodemon src/index.ts",
    "build": "tsc",
    "start": "node dist/index.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "helmet": "^7.1.0"
  },
  "devDependencies": {
    "@types/express": "^4.17.21",
    "@types/node": "^20.10.0",
    "@types/cors": "^2.8.17",
    "typescript": "^5.3.3",
    "nodemon": "^3.0.2",
    "ts-node": "^10.9.2"
  }
}
EOF

# Create TypeScript config
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "lib": ["es2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
EOF

# Create basic server
mkdir -p src
cat > src/index.ts << 'EOF'
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3001'
}));
app.use(express.json());

app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'agentised-backend' });
});

app.get('/api/agents', (req, res) => {
  res.json({ agents: [], message: 'Agent system ready' });
});

app.listen(PORT, () => {
  console.log(`🚀 Backend running on port ${PORT}`);
});
EOF

# Create .env.example
cat > .env.example << 'EOF'
PORT=5000
FRONTEND_URL=http://localhost:3001
DATABASE_URL=
JWT_SECRET=
EOF

git add -A
git commit -m "Initialize backend with Express and TypeScript"
git push origin main

echo "✅ Backend initialized!"

# 4. Set up shared utilities
echo "📦 Setting up shared utilities..."
cd ~/agentised/shared

# Create package.json
cat > package.json << 'EOF'
{
  "name": "@agentised/shared",
  "version": "1.0.0",
  "description": "Shared types and utilities",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "prepublish": "npm run build"
  },
  "devDependencies": {
    "typescript": "^5.3.3"
  }
}
EOF

# TypeScript config
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "declaration": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true
  },
  "include": ["src/**/*"]
}
EOF

# Create shared types
mkdir -p src
cat > src/index.ts << 'EOF'
// Agent types
export interface Agent {
  id: string;
  name: string;
  description: string;
  capabilities: string[];
  status: 'active' | 'inactive' | 'training';
  createdAt: Date;
  updatedAt: Date;
}

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user' | 'developer';
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}
EOF

git add -A
git commit -m "Add shared types and utilities"
git push origin main

echo "✅ Shared utilities ready!"

echo ""
echo "🎉 Migration complete! Your agentised repositories are ready."
echo ""
echo "📁 Repository structure:"
echo "  • landingpage - Your marketing site (migrated)"
echo "  • frontend - Main app (initialized)"
echo "  • backend - API server (initialized)"
echo "  • shared - Shared types (initialized)"
echo ""
echo "🚀 Next steps:"
echo "  1. cd ~/agentised/backend && npm install"
echo "  2. cd ~/agentised/frontend && npm install"
echo "  3. Start development:"
echo "     - Terminal 1: cd ~/agentised/backend && npm run dev"
echo "     - Terminal 2: cd ~/agentised/frontend && npm run dev"